// Content script to handle proxy auth popups
(function() {
    'use strict';
    
    // Function to close any auth dialogs
    function closeAuthDialogs() {
        // Look for common auth dialog patterns
        const authDialogs = document.querySelectorAll([
            'dialog[open]',
            '.auth-dialog',
            '[role="dialog"]',
            '.modal',
            '.popup'
        ].join(','));
        
        authDialogs.forEach(dialog => {
            // Look for cancel/close buttons
            const closeButtons = dialog.querySelectorAll([
                'button[data-action="cancel"]',
                'button[aria-label*="cancel"]',
                'button[aria-label*="close"]',
                '.cancel-btn',
                '.close-btn',
                'button:contains("Cancel")',
                'button:contains("Close")'
            ].join(','));
            
            if (closeButtons.length > 0) {
                closeButtons[0].click();
                console.log('Closed auth dialog');
            } else {
                // Try to hide the dialog
                dialog.style.display = 'none';
                dialog.remove();
                console.log('Removed auth dialog');
            }
        });
    }
    
    // Monitor for auth popups
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.addedNodes.length > 0) {
                // Check if any new nodes contain auth dialogs
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Check if this is an auth dialog
                        if (node.matches && (
                            node.matches('dialog') ||
                            node.matches('[role="dialog"]') ||
                            node.querySelector && (
                                node.querySelector('input[type="password"]') ||
                                node.textContent.toLowerCase().includes('proxy') ||
                                node.textContent.toLowerCase().includes('authentication')
                            )
                        )) {
                            setTimeout(closeAuthDialogs, 100);
                        }
                    }
                });
            }
        });
    });
    
    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    // Check for existing dialogs on load
    document.addEventListener('DOMContentLoaded', closeAuthDialogs);
    
    // Periodic check
    setInterval(closeAuthDialogs, 1000);
    
    console.log('Proxy auth content script loaded');
})();
