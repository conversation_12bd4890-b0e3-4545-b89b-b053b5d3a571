// Background script to handle proxy authentication
let proxyCredentials = null;

// Load credentials when extension starts
chrome.runtime.onStartup.addListener(loadCredentials);
chrome.runtime.onInstalled.addListener(loadCredentials);

function loadCredentials() {
  chrome.storage.local.get(['proxyUsername', 'proxyPassword'], function(result) {
    if (result.proxyUsername && result.proxyPassword) {
      proxyCredentials = {
        username: result.proxyUsername,
        password: result.proxyPassword
      };
      console.log('Proxy credentials loaded:', result.proxyUsername);
    }
  });
}

// Listen for storage changes
chrome.storage.onChanged.addListener(function(changes, namespace) {
  if (namespace === 'local' && (changes.proxyUsername || changes.proxyPassword)) {
    loadCredentials();
  }
});

// Handle proxy authentication
chrome.webRequest.onAuthRequired.addListener(
  function(details) {
    console.log('Auth required for:', details.url);
    
    if (proxyCredentials) {
      console.log('Providing credentials for:', proxyCredentials.username);
      return {
        authCredentials: {
          username: proxyCredentials.username,
          password: proxyCredentials.password
        }
      };
    }
    
    console.log('No credentials available');
    return {};
  },
  {urls: ["<all_urls>"]},
  ["blocking"]
);

// Load credentials immediately
loadCredentials();
console.log('Proxy Auth Handler loaded');
