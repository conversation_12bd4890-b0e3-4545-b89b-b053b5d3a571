#!/usr/bin/env python3

import os
import sys
import importlib.util
from pathlib import Path

# Import the module with numeric name
spec = importlib.util.spec_from_file_location("gmail_automation", "2.py")
gmail_automation = importlib.util.module_from_spec(spec)
spec.loader.exec_module(gmail_automation)

GmailAutomation = gmail_automation.GmailAutomation

def test_proxy_auth():
    """Test proxy authentication with a sample account"""
    
    # Sample account data from Accounts.txt
    account = {
        'email': '<EMAIL>',
        'password': 'Salah2005@',
        'proxy': '85.143.172.42',
        'port': '3128',
        'proxyUsername': 'admin123',
        'proxyPassword': 'Hello123',
        'recovry': '<EMAIL>'
    }
    
    print("Testing proxy authentication...")
    print(f"Account: {account['email']}")
    print(f"Proxy: {account['proxyUsername']}@{account['proxy']}:{account['port']}")
    
    # Create automation instance
    automation = GmailAutomation(basedir='.')
    
    # Test driver creation
    driver = automation.create_driver(account)
    
    if driver:
        print("✅ Driver created successfully!")
        
        try:
            # Test basic navigation
            print("Testing navigation to Google...")
            driver.get('https://www.google.com')

            # Wait a bit
            automation.sleep(5)

            print(f"Current URL: {driver.current_url}")
            print(f"Page title: {driver.title}")

            # Test IP check with multiple services
            ip_services = [
                'https://httpbin.org/ip',
                'https://api.ipify.org?format=json',
                'https://ifconfig.me/ip'
            ]

            for service in ip_services:
                try:
                    print(f"Testing IP check with {service}...")
                    driver.get(service)
                    automation.sleep(5)

                    page_source = driver.page_source
                    print(f"Response from {service}: {page_source[:300]}...")

                    if 'origin' in page_source or 'ip' in page_source or len(page_source.strip()) > 5:
                        print("✅ Got valid response")
                        break
                    else:
                        print("❌ Empty or invalid response")

                except Exception as e:
                    print(f"❌ Failed to test {service}: {e}")

            # Test Gmail login page
            print("Testing Gmail access...")
            driver.get('https://accounts.google.com')
            automation.sleep(5)
            print(f"Gmail page title: {driver.title}")

        except Exception as e:
            print(f"❌ Navigation test failed: {e}")
        
        finally:
            print("Closing driver...")
            driver.quit()
    else:
        print("❌ Failed to create driver")

if __name__ == "__main__":
    test_proxy_auth()
