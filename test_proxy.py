#!/usr/bin/env python3

import os
import sys
import importlib.util
from pathlib import Path

# Import the module with numeric name
spec = importlib.util.spec_from_file_location("gmail_automation", "2.py")
gmail_automation = importlib.util.module_from_spec(spec)
spec.loader.exec_module(gmail_automation)

GmailAutomation = gmail_automation.GmailAutomation

def test_proxy_auth():
    """Test proxy authentication with a sample account"""
    
    # Sample account data from Accounts.txt
    account = {
        'email': '<EMAIL>',
        'password': 'Salah2005@',
        'proxy': '85.143.172.42',
        'port': '3128',
        'proxyUsername': 'admin123',
        'proxyPassword': 'Hello123',
        'recovry': '<EMAIL>'
    }
    
    print("Testing proxy authentication...")
    print(f"Account: {account['email']}")
    print(f"Proxy: {account['proxyUsername']}@{account['proxy']}:{account['port']}")
    
    # Create automation instance
    automation = GmailAutomation(basedir='.')
    
    # Test driver creation
    driver = automation.create_driver(account)
    
    if driver:
        print("✅ Driver created successfully!")
        
        try:
            # Test basic navigation
            print("Testing navigation to Google...")
            driver.get('https://www.google.com')

            # Wait a bit
            automation.sleep(5)

            print(f"Current URL: {driver.current_url}")
            print(f"Page title: {driver.title}")

            # Test IP check with multiple services
            ip_services = [
                'https://httpbin.org/ip',
                'https://api.ipify.org?format=json',
                'https://ifconfig.me/ip',
                'https://icanhazip.com',
                'https://checkip.amazonaws.com'
            ]

            proxy_working = False
            for service in ip_services:
                try:
                    print(f"Testing IP check with {service}...")
                    driver.get(service)
                    automation.sleep(8)

                    page_source = driver.page_source
                    print(f"Response from {service}: {page_source[:300]}...")

                    # Check if we got a valid IP response
                    if ('origin' in page_source or 'ip' in page_source or
                        any(char.isdigit() for char in page_source) and '.' in page_source):
                        print("✅ Got valid IP response")
                        proxy_working = True

                        # Extract IP if possible
                        import re
                        ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
                        ips = re.findall(ip_pattern, page_source)
                        if ips:
                            print(f"Detected IP: {ips[0]}")
                        break
                    else:
                        print("❌ Empty or invalid response")

                except Exception as e:
                    print(f"❌ Failed to test {service}: {e}")

            if not proxy_working:
                print("⚠️  Proxy might not be working correctly - all IP services returned empty responses")
                print("This could mean:")
                print("1. Proxy server is down or unreachable")
                print("2. Proxy credentials are incorrect")
                print("3. Proxy is blocking these IP check services")
                print("4. Network connectivity issues")

            # Test Gmail login page
            print("Testing Gmail access...")
            driver.get('https://accounts.google.com')
            automation.sleep(5)
            print(f"Gmail page title: {driver.title}")

        except Exception as e:
            print(f"❌ Navigation test failed: {e}")
        
        finally:
            print("Closing driver...")
            driver.quit()
    else:
        print("❌ Failed to create driver")

if __name__ == "__main__":
    test_proxy_auth()
