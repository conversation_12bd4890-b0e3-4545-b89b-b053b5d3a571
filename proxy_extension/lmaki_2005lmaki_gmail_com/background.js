
var config = {
    mode: "fixed_servers",
    rules: {
        singleProxy: {
            scheme: "http",
            host: "*************",
            port: parseInt(3128)
        },
        bypassList: ["localhost", "127.0.0.1"]
    }
};

chrome.proxy.settings.set({value: config, scope: "regular"}, function() {
    console.log("Proxy configured successfully for *************:3128");
});

function callbackFn(details) {
    console.log("Proxy authentication requested for:", details.url);
    console.log("Using credentials: admin123");
    return {
        authCredentials: {
            username: "admin123",
            password: "Hello123"
        }
    };
}

chrome.webRequest.onAuthRequired.addListener(
    callbackFn,
    {urls: ["<all_urls>"]},
    ['blocking']
);

// Additional debugging
chrome.webRequest.onBeforeRequest.addListener(
    function(details) {
        console.log("Request to:", details.url);
    },
    {urls: ["<all_urls>"]},
    []
);

chrome.webRequest.onErrorOccurred.addListener(
    function(details) {
        console.log("Request error:", details.error, "for URL:", details.url);
    },
    {urls: ["<all_urls>"]}
);
